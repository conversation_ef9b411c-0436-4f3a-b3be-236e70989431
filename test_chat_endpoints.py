#!/usr/bin/env python3
"""
Comprehensive test script for HTTP-based Chat API Endpoints
This script will:
1. Start a streaming session to get a real session ID
2. Test chat/send endpoint with the session ID
3. Test chat/history endpoint with the session ID
4. Verify session isolation
5. Test error handling
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8012"
HEADERS = {"Content-Type": "application/json"}

def print_separator(title):
    """Print a formatted separator with title"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_test_result(test_name, success, response_data=None, error=None):
    """Print formatted test results"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if response_data:
        print(f"   Response: {json.dumps(response_data, indent=2)}")
    if error:
        print(f"   Error: {error}")
    print()

def test_health_check():
    """Test if the server is running"""
    print_separator("HEALTH CHECK")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print_test_result("Health Check", True, {
                "status": data.get("status"),
                "service": data.get("service"),
                "active_streams": data.get("active_streams"),
                "livekit_status": data.get("livekit", {}).get("status")
            })
            return True
        else:
            print_test_result("Health Check", False, error=f"Status code: {response.status_code}")
            return False
    except Exception as e:
        print_test_result("Health Check", False, error=str(e))
        return False

def start_streaming_session():
    """Start a new streaming session and return session ID"""
    print_separator("STARTING STREAMING SESSION")
    
    payload = {
        "teacher_id": "test_teacher_123",
        "teacher_name": "Test Teacher",
        "quality": "medium"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/enhanced-stream/start",
            headers=HEADERS,
            json=payload,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            print_test_result("Start Streaming Session", True, {
                "session_id": session_id,
                "quality": data.get("quality"),
                "livekit_url": data.get("livekit_url"),
                "message": data.get("message")
            })
            return session_id
        else:
            print_test_result("Start Streaming Session", False, 
                            error=f"Status code: {response.status_code}, Response: {response.text}")
            return None
            
    except Exception as e:
        print_test_result("Start Streaming Session", False, error=str(e))
        return None

def test_send_chat_message(session_id, message, sender_id, sender_name):
    """Test sending a chat message"""
    payload = {
        "session_id": session_id,
        "message": message,
        "sender_id": sender_id,
        "sender_name": sender_name
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/chat/send",
            headers=HEADERS,
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"Status code: {response.status_code}, Response: {response.text}"
            
    except Exception as e:
        return False, str(e)

def test_get_chat_history(session_id):
    """Test getting chat history"""
    try:
        response = requests.get(
            f"{BASE_URL}/api/chat/history",
            params={"session_id": session_id},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"Status code: {response.status_code}, Response: {response.text}"
            
    except Exception as e:
        return False, str(e)

def test_chat_endpoints_with_session(session_id):
    """Test all chat endpoints with a real session ID"""
    print_separator(f"TESTING CHAT ENDPOINTS WITH SESSION: {session_id}")
    
    # Test 1: Send first chat message
    success, result = test_send_chat_message(
        session_id, 
        "Hello! This is the first test message in our streaming session.", 
        "teacher_123", 
        "Test Teacher"
    )
    print_test_result("Send Chat Message #1", success, result if success else None, result if not success else None)
    
    # Test 2: Send second chat message
    success, result = test_send_chat_message(
        session_id, 
        "This is the second message to test multiple messages.", 
        "student_456", 
        "Test Student"
    )
    print_test_result("Send Chat Message #2", success, result if success else None, result if not success else None)
    
    # Test 3: Send third chat message with emoji
    success, result = test_send_chat_message(
        session_id, 
        "Great session! 👍 Looking forward to more content! 🎓", 
        "student_789", 
        "Another Student"
    )
    print_test_result("Send Chat Message #3 (with emoji)", success, result if success else None, result if not success else None)
    
    # Wait a moment for messages to be processed
    time.sleep(1)
    
    # Test 4: Get chat history
    success, result = test_get_chat_history(session_id)
    if success:
        messages = result.get("messages", [])
        print_test_result("Get Chat History", True, {
            "session_id": result.get("session_id"),
            "message_count": len(messages),
            "messages": [
                {
                    "id": msg.get("id"),
                    "sender_name": msg.get("sender_name"),
                    "message": msg.get("message")[:50] + "..." if len(msg.get("message", "")) > 50 else msg.get("message"),
                    "timestamp": msg.get("timestamp")
                } for msg in messages
            ]
        })
        return len(messages)
    else:
        print_test_result("Get Chat History", False, error=result)
        return 0

def test_error_handling(session_id):
    """Test error handling scenarios"""
    print_separator("TESTING ERROR HANDLING")
    
    # Test 1: Send message without required fields
    try:
        response = requests.post(
            f"{BASE_URL}/api/chat/send",
            headers=HEADERS,
            json={"session_id": session_id, "message": "Missing sender_id"},
            timeout=10
        )
        success = response.status_code == 400
        print_test_result("Missing sender_id validation", success, 
                        {"status_code": response.status_code, "response": response.json()} if success else None,
                        "Expected 400 status code" if not success else None)
    except Exception as e:
        print_test_result("Missing sender_id validation", False, error=str(e))
    
    # Test 2: Get history without session_id
    try:
        response = requests.get(f"{BASE_URL}/api/chat/history", timeout=10)
        success = response.status_code == 400
        print_test_result("Missing session_id in history", success,
                        {"status_code": response.status_code, "response": response.json()} if success else None,
                        "Expected 400 status code" if not success else None)
    except Exception as e:
        print_test_result("Missing session_id in history", False, error=str(e))

def test_session_isolation():
    """Test that messages are isolated between sessions"""
    print_separator("TESTING SESSION ISOLATION")
    
    # Create a second session
    second_session_id = start_streaming_session()
    if not second_session_id:
        print_test_result("Session Isolation Test", False, error="Could not create second session")
        return
    
    # Send message to second session
    success, result = test_send_chat_message(
        second_session_id,
        "This message is in a different session",
        "user_different",
        "Different User"
    )
    
    if success:
        # Get history for second session
        success2, result2 = test_get_chat_history(second_session_id)
        if success2:
            messages = result2.get("messages", [])
            print_test_result("Session Isolation", len(messages) == 1, {
                "second_session_id": second_session_id,
                "message_count": len(messages),
                "isolation_verified": len(messages) == 1
            })
        else:
            print_test_result("Session Isolation", False, error="Could not get history for second session")
    else:
        print_test_result("Session Isolation", False, error="Could not send message to second session")

def main():
    """Main test function"""
    print_separator("HTTP-BASED CHAT API ENDPOINTS TEST")
    print(f"Testing server at: {BASE_URL}")
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Step 1: Health check
    if not test_health_check():
        print("❌ Server is not running or not responding. Please start the server first.")
        sys.exit(1)
    
    # Step 2: Start streaming session
    session_id = start_streaming_session()
    if not session_id:
        print("❌ Could not start streaming session. Cannot proceed with chat tests.")
        sys.exit(1)
    
    # Step 3: Test chat endpoints with real session
    message_count = test_chat_endpoints_with_session(session_id)
    
    # Step 4: Test error handling
    test_error_handling(session_id)
    
    # Step 5: Test session isolation
    test_session_isolation()
    
    # Final summary
    print_separator("TEST SUMMARY")
    print(f"✅ Server is running and responding")
    print(f"✅ Streaming session created: {session_id}")
    print(f"✅ Chat messages sent and stored: {message_count} messages")
    print(f"✅ Chat history retrieval working")
    print(f"✅ Error handling working")
    print(f"✅ Session isolation working")
    print(f"\n🎉 All HTTP-based Chat API Endpoints are working correctly!")
    print(f"Test completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
